"""
Optimized testing and evaluation module for Driver Anomaly Detection.

This module provides comprehensive testing functionality including:
- Normal vector calculation from training data
- Threshold optimization for anomaly detection
- Multi-modal score calculation and fusion
- Performance evaluation with detailed metrics
"""

import os
from typing import Dict, List, Tuple, Optional, Union
import warnings

import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader

from utils import l2_normalize, post_process_scores, evaluate_scores


class NormalVectorCalculator:
    """Calculator for computing normal driving behavior template vectors."""
    
    def __init__(self, feature_dim: int = 512, use_cuda: bool = True):
        """
        Initialize normal vector calculator.
        
        Args:
            feature_dim (int): Dimension of feature vectors
            use_cuda (bool): Whether to use CUDA acceleration
        """
        self.feature_dim = feature_dim
        self.use_cuda = use_cuda and torch.cuda.is_available()
        self.device = torch.device('cuda' if self.use_cuda else 'cpu')
    
    def calculate_normal_vector(self, model: nn.Module, normal_loader: DataLoader, 
                              batch_size: int) -> torch.Tensor:
        """
        Calculate average normal vector from training data.
        
        Args:
            model (nn.Module): Trained model for feature extraction
            normal_loader (DataLoader): DataLoader containing normal driving samples
            batch_size (int): Batch size for calculation
            
        Returns:
            torch.Tensor: L2-normalized normal vector template
        """
        print("=" * 80)
        print("Calculating Average Normal Vector")
        print("=" * 80)
        
        model.eval()
        total_batches = len(normal_loader)
        
        # Initialize accumulator
        normal_vec = torch.zeros((1, self.feature_dim), device=self.device)
        total_samples = 0
        
        with torch.no_grad():
            for batch_idx, (normal_data, _) in enumerate(normal_loader):
                try:
                    # Move data to device
                    normal_data = normal_data.to(self.device)
                    
                    # Extract features
                    _, features = model(normal_data)
                    features = features.detach()
                    
                    # Update running average
                    batch_samples = features.size(0)
                    batch_sum = torch.sum(features, dim=0, keepdim=True)
                    
                    # Incremental average calculation
                    normal_vec = (normal_vec * total_samples + batch_sum) / (total_samples + batch_samples)
                    total_samples += batch_samples
                    
                    # Progress reporting
                    progress = (batch_idx + 1) / total_batches * 100
                    print(f'Progress: {batch_idx + 1}/{total_batches} ({progress:.1f}%) - '
                          f'Samples processed: {total_samples}')
                
                except Exception as e:
                    print(f"Warning: Error processing batch {batch_idx}: {e}")
                    continue
        
        if total_samples == 0:
            raise ValueError("No valid samples processed for normal vector calculation")
        
        # L2 normalize the final vector
        normal_vec = l2_normalize(normal_vec)
        
        print(f"Normal vector calculation completed. Total samples: {total_samples}")
        print(f"Normal vector shape: {normal_vec.shape}")
        print("=" * 80)
        
        return normal_vec


class ThresholdOptimizer:
    """Optimizer for finding the best threshold for anomaly detection."""
    
    def __init__(self, threshold_range: Tuple[float, float] = (0.0, 1.0), 
                 threshold_step: float = 0.01, use_cuda: bool = True):
        """
        Initialize threshold optimizer.
        
        Args:
            threshold_range (Tuple[float, float]): Range of thresholds to test
            threshold_step (float): Step size for threshold search
            use_cuda (bool): Whether to use CUDA acceleration
        """
        self.threshold_range = threshold_range
        self.threshold_step = threshold_step
        self.use_cuda = use_cuda and torch.cuda.is_available()
        self.device = torch.device('cuda' if self.use_cuda else 'cpu')
        
        # Generate threshold array
        self.thresholds = np.arange(threshold_range[0], threshold_range[1], threshold_step)
    
    def optimize_threshold(self, model: nn.Module, normal_vec: torch.Tensor, 
                          test_loader: DataLoader) -> Dict[str, Union[float, np.ndarray]]:
        """
        Find optimal threshold by testing different values.
        
        Args:
            model (nn.Module): Trained model for feature extraction
            normal_vec (torch.Tensor): Normal vector template
            test_loader (DataLoader): Test data loader
            
        Returns:
            Dict containing optimization results
        """
        print("=" * 80)
        print("Optimizing Detection Threshold")
        print("=" * 80)
        
        model.eval()
        total_batches = len(test_loader)
        
        # Initialize counters
        total_normal = 0
        total_anomalous = 0
        correct_normal = np.zeros(len(self.thresholds))
        correct_anomalous = np.zeros(len(self.thresholds))
        
        with torch.no_grad():
            for batch_idx, (data, labels) in enumerate(test_loader):
                try:
                    # Move data to device
                    data = data.to(self.device)
                    labels = labels.to(self.device)
                    
                    # Count samples
                    normal_mask = labels.bool()
                    batch_normal = torch.sum(normal_mask).item()
                    batch_anomalous = data.size(0) - batch_normal
                    
                    total_normal += batch_normal
                    total_anomalous += batch_anomalous
                    
                    # Extract features and compute similarities
                    _, features = model(data)
                    features = features.detach()
                    similarities = torch.mm(features, normal_vec.t()).squeeze()
                    
                    # Test all thresholds
                    for i, threshold in enumerate(self.thresholds):
                        # Predictions: similarity >= threshold means normal
                        predictions = similarities >= threshold
                        correct_predictions = predictions == labels
                        
                        # Count correct predictions for each class
                        correct_normal[i] += torch.sum(correct_predictions[normal_mask]).item()
                        correct_anomalous[i] += torch.sum(correct_predictions[~normal_mask]).item()
                    
                    # Progress reporting
                    progress = (batch_idx + 1) / total_batches * 100
                    print(f'Progress: {batch_idx + 1}/{total_batches} ({progress:.1f}%)')
                
                except Exception as e:
                    print(f"Warning: Error processing batch {batch_idx}: {e}")
                    continue
        
        if total_normal == 0 or total_anomalous == 0:
            raise ValueError("Invalid test set: missing normal or anomalous samples")
        
        # Calculate accuracies
        accuracy_normal = correct_normal / total_normal if total_normal > 0 else np.zeros_like(correct_normal)
        accuracy_anomalous = correct_anomalous / total_anomalous if total_anomalous > 0 else np.zeros_like(correct_anomalous)
        overall_accuracy = (correct_normal + correct_anomalous) / (total_normal + total_anomalous)
        
        # Find best threshold
        best_idx = np.argmax(overall_accuracy)
        best_threshold = self.thresholds[best_idx]
        best_accuracy = overall_accuracy[best_idx]
        
        results = {
            'best_accuracy': best_accuracy,
            'best_threshold': best_threshold,
            'normal_accuracy': accuracy_normal[best_idx],
            'anomalous_accuracy': accuracy_anomalous[best_idx],
            'all_accuracies': overall_accuracy,
            'all_normal_accuracies': accuracy_normal,
            'all_anomalous_accuracies': accuracy_anomalous,
            'thresholds': self.thresholds,
            'total_normal': total_normal,
            'total_anomalous': total_anomalous
        }
        
        print(f"Threshold optimization completed:")
        print(f"  Best threshold: {best_threshold:.3f}")
        print(f"  Best accuracy: {best_accuracy:.3f}")
        print(f"  Normal accuracy: {accuracy_normal[best_idx]:.3f}")
        print(f"  Anomalous accuracy: {accuracy_anomalous[best_idx]:.3f}")
        print("=" * 80)
        
        return results


class MultiModalScoreCalculator:
    """Calculator for multi-modal anomaly scores."""
    
    def __init__(self, use_cuda: bool = True):
        """
        Initialize multi-modal score calculator.
        
        Args:
            use_cuda (bool): Whether to use CUDA acceleration
        """
        self.use_cuda = use_cuda and torch.cuda.is_available()
        self.device = torch.device('cuda' if self.use_cuda else 'cpu')
    
    def calculate_scores(self, models: Dict[str, nn.Module], 
                        normal_vectors: Dict[str, torch.Tensor],
                        test_loaders: Dict[str, DataLoader],
                        score_folder: str) -> Dict[str, np.ndarray]:
        """
        Calculate and save scores for all modalities.
        
        Args:
            models (Dict[str, nn.Module]): Models for each view
            normal_vectors (Dict[str, torch.Tensor]): Normal vectors for each view
            test_loaders (Dict[str, DataLoader]): Test loaders for each view
            score_folder (str): Folder to save score files
            
        Returns:
            Dict[str, np.ndarray]: Calculated scores for each modality
        """
        print("=" * 80)
        print("Calculating Multi-Modal Scores")
        print("=" * 80)
        
        # Validate inputs
        views = ['front_depth', 'front_IR', 'top_depth', 'top_IR']
        self._validate_inputs(models, normal_vectors, test_loaders, views)
        
        # Create output directory
        os.makedirs(score_folder, exist_ok=True)
        
        # Initialize score storage
        scores = {view: [] for view in views}
        labels = []
        
        # Get total batches (assuming all loaders have same length)
        total_batches = len(next(iter(test_loaders.values())))
        
        # Set models to evaluation mode
        for model in models.values():
            model.eval()
        
        with torch.no_grad():
            # Iterate through all loaders simultaneously
            loader_iters = {view: iter(loader) for view, loader in test_loaders.items()}
            
            for batch_idx in range(total_batches):
                try:
                    batch_data = {}
                    
                    # Load data from all views
                    for view in views:
                        data, batch_labels = next(loader_iters[view])
                        batch_data[view] = (data.to(self.device), batch_labels.to(self.device))
                    
                    # Validate labels consistency across views
                    self._validate_label_consistency(batch_data, views)
                    
                    # Calculate scores for each view
                    batch_scores = {}
                    for view in views:
                        data, _ = batch_data[view]
                        
                        # Extract features
                        _, features = models[view](data)
                        features = features.detach()
                        
                        # Calculate similarity scores
                        similarities = torch.mm(features, normal_vectors[view].t())
                        batch_scores[view] = similarities.squeeze().cpu()
                    
                    # Store scores and labels
                    for view in views:
                        scores[view].append(batch_scores[view])
                    
                    # Store labels (same for all views)
                    labels.append(batch_data[views[0]][1].cpu())
                    
                    # Progress reporting
                    progress = (batch_idx + 1) / total_batches * 100
                    print(f'Progress: {batch_idx + 1}/{total_batches} ({progress:.1f}%)')
                
                except Exception as e:
                    print(f"Warning: Error processing batch {batch_idx}: {e}")
                    continue
        
        # Concatenate all scores
        final_scores = {}
        for view in views:
            if scores[view]:
                final_scores[view] = torch.cat(scores[view]).numpy()
            else:
                print(f"Warning: No valid scores for view {view}")
                final_scores[view] = np.array([])
        
        # Calculate fusion score
        if all(len(final_scores[view]) > 0 for view in views):
            fusion_score = np.mean([final_scores[view] for view in views], axis=0)
            final_scores['fusion'] = fusion_score
        
        # Save scores to files
        self._save_scores(final_scores, score_folder)
        
        print("Multi-modal score calculation completed!")
        print("=" * 80)
        
        return final_scores
    
    def _validate_inputs(self, models: Dict, normal_vectors: Dict, 
                        test_loaders: Dict, views: List[str]):
        """Validate input dictionaries have all required views."""
        for view in views:
            if view not in models:
                raise ValueError(f"Missing model for view: {view}")
            if view not in normal_vectors:
                raise ValueError(f"Missing normal vector for view: {view}")
            if view not in test_loaders:
                raise ValueError(f"Missing test loader for view: {view}")
    
    def _validate_label_consistency(self, batch_data: Dict, views: List[str]):
        """Validate that labels are consistent across all views."""
        reference_labels = batch_data[views[0]][1]
        
        for view in views[1:]:
            current_labels = batch_data[view][1]
            if not torch.equal(reference_labels, current_labels):
                raise ValueError(f"Label mismatch between {views[0]} and {view}")
    
    def _save_scores(self, scores: Dict[str, np.ndarray], score_folder: str):
        """Save scores to numpy files."""
        view_mapping = {
            'front_depth': 'score_front_d.npy',
            'front_IR': 'score_front_IR.npy', 
            'top_depth': 'score_top_d.npy',
            'top_IR': 'score_top_IR.npy',
            'fusion': 'score_fusion.npy'
        }
        
        for view, filename in view_mapping.items():
            if view in scores and len(scores[view]) > 0:
                filepath = os.path.join(score_folder, filename)
                np.save(filepath, scores[view])
                print(f"Saved: {filename}")


# ============================================================================
# BACKWARD COMPATIBILITY FUNCTIONS
# ============================================================================

def get_normal_vector(model: nn.Module, train_normal_loader_for_test: DataLoader,
                     cal_vec_batch_size: int, feature_dim: int, use_cuda: bool) -> torch.Tensor:
    """
    Legacy function for backward compatibility.
    
    Calculate normal vector using the optimized calculator.
    """
    calculator = NormalVectorCalculator(feature_dim, use_cuda)
    return calculator.calculate_normal_vector(model, train_normal_loader_for_test, cal_vec_batch_size)


def split_acc_diff_threshold(model: nn.Module, normal_vec: torch.Tensor,
                           test_loader: DataLoader, use_cuda: bool) -> Tuple:
    """
    Legacy function for backward compatibility.
    
    Find optimal threshold using the optimized optimizer.
    """
    optimizer = ThresholdOptimizer(use_cuda=use_cuda)
    results = optimizer.optimize_threshold(model, normal_vec, test_loader)
    
    return (
        results['best_accuracy'],
        results['best_threshold'], 
        results['normal_accuracy'],
        results['anomalous_accuracy'],
        results['all_accuracies'],
        results['all_normal_accuracies'],
        results['all_anomalous_accuracies']
    )


def cal_score(model_front_d: nn.Module, model_front_ir: nn.Module,
              model_top_d: nn.Module, model_top_ir: nn.Module,
              normal_vec_front_d: torch.Tensor, normal_vec_front_ir: torch.Tensor,
              normal_vec_top_d: torch.Tensor, normal_vec_top_ir: torch.Tensor,
              test_loader_front_d: DataLoader, test_loader_front_ir: DataLoader,
              test_loader_top_d: DataLoader, test_loader_top_ir: DataLoader,
              score_folder: str, use_cuda: bool):
    """
    Legacy function for backward compatibility.
    
    Calculate multi-modal scores using the optimized calculator.
    """
    models = {
        'front_depth': model_front_d,
        'front_IR': model_front_ir,
        'top_depth': model_top_d,
        'top_IR': model_top_ir
    }
    
    normal_vectors = {
        'front_depth': normal_vec_front_d,
        'front_IR': normal_vec_front_ir,
        'top_depth': normal_vec_top_d,
        'top_IR': normal_vec_top_ir
    }
    
    test_loaders = {
        'front_depth': test_loader_front_d,
        'front_IR': test_loader_front_ir,
        'top_depth': test_loader_top_d,
        'top_IR': test_loader_top_ir
    }
    
    calculator = MultiModalScoreCalculator(use_cuda)
    return calculator.calculate_scores(models, normal_vectors, test_loaders, score_folder)


# ============================================================================
# TESTING
# ============================================================================

def test_components():
    """Test the optimized testing components."""
    print("Testing optimized test components...")
    
    # Test NormalVectorCalculator
    calculator = NormalVectorCalculator(feature_dim=128, use_cuda=False)
    print(f"✅ NormalVectorCalculator initialized: {type(calculator)}")
    
    # Test ThresholdOptimizer
    optimizer = ThresholdOptimizer(use_cuda=False)
    print(f"✅ ThresholdOptimizer initialized: {type(optimizer)}")
    print(f"   Threshold range: {optimizer.threshold_range}")
    print(f"   Number of thresholds: {len(optimizer.thresholds)}")
    
    # Test MultiModalScoreCalculator
    score_calc = MultiModalScoreCalculator(use_cuda=False)
    print(f"✅ MultiModalScoreCalculator initialized: {type(score_calc)}")
    
    print("All test components initialized successfully!")


if __name__ == '__main__':
    test_components()
