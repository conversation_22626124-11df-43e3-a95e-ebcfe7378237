"""
Main training and testing script for Driver Anomaly Detection.

This script provides a simplified and well-structured implementation for:
- Training models with contrastive learning
- Testing and evaluation
- Configuration management
- Logging and checkpointing
"""

import argparse
import ast
import os
from typing import Dict, Any, Tuple, Optional

import numpy as np
import torch
import torch.backends.cudnn as cudnn
from torch.utils.data import DataLoader

# Local imports
from dataset import DAD
from dataset_test import DAD_Test
from model import generate_model
from NCEAverage import NCEAverage
from NCECriterion import NCECriterion
from utils import (
    adjust_learning_rate, AverageMeter, Logger, 
    l2_normalize, post_process_scores, evaluate_scores, ScoreFusion
)
import spatial_transforms
from temporal_transforms import TemporalSequentialCrop


class DADConfig:
    """Configuration class for Driver Anomaly Detection."""
    
    @staticmethod
    def create_parser() -> argparse.ArgumentParser:
        """Create argument parser with all configuration options."""
        parser = argparse.ArgumentParser(description='Driver Anomaly Detection Training and Testing')
        
        # Dataset and paths
        parser.add_argument('--root_path', default='', type=str, 
                          help='Root path of the dataset')
        parser.add_argument('--checkpoint_folder', default='./checkpoints', type=str,
                          help='Folder to save checkpoints')
        parser.add_argument('--log_folder', default='./logs', type=str,
                          help='Folder to save logs')
        parser.add_argument('--score_folder', default='./scores', type=str,
                          help='Folder to save scores')
        
        # Mode and view
        parser.add_argument('--mode', default='train', type=str, 
                          choices=['train', 'test'], help='Training or testing mode')
        parser.add_argument('--view', default='front_IR', type=str,
                          choices=['front_depth', 'front_IR', 'top_depth', 'top_IR'],
                          help='Camera view and modality')
        
        # Model configuration
        parser.add_argument('--model_type', default='resnet', type=str,
                          choices=['resnet', 'shufflenet', 'shufflenetv2', 'mobilenet', 'mobilenetv2'],
                          help='Model architecture type')
        parser.add_argument('--model_depth', default=18, type=int,
                          choices=[18, 50, 101], help='Model depth (for ResNet)')
        parser.add_argument('--feature_dim', default=128, type=int,
                          help='Feature embedding dimension')
        parser.add_argument('--shortcut_type', default='B', type=str,
                          choices=['A', 'B'], help='ResNet shortcut type')
        
        # Data configuration
        parser.add_argument('--sample_duration', default=16, type=int,
                          help='Temporal duration of video clips')
        parser.add_argument('--sample_size', default=112, type=int,
                          help='Spatial size of video frames')
        parser.add_argument('--norm_value', default=255, type=int,
                          choices=[1, 255], help='Normalization value for inputs')
        
        # Training configuration
        parser.add_argument('--pre_train_model', default=True, type=ast.literal_eval,
                          help='Whether to use pre-trained model')
        parser.add_argument('--n_train_batch_size', default=3, type=int,
                          help='Batch size for normal training data')
        parser.add_argument('--a_train_batch_size', default=25, type=int,
                          help='Batch size for anomalous training data')
        parser.add_argument('--val_batch_size', default=25, type=int,
                          help='Batch size for validation data')
        parser.add_argument('--learning_rate', default=0.01, type=float,
                          help='Initial learning rate')
        parser.add_argument('--momentum', default=0.9, type=float,
                          help='SGD momentum')
        parser.add_argument('--weight_decay', default=1e-4, type=float,
                          help='Weight decay')
        parser.add_argument('--epochs', default=250, type=int,
                          help='Number of training epochs')
        
        # NCE configuration
        parser.add_argument('--tau', default=0.1, type=float,
                          help='Temperature parameter for NCE')
        parser.add_argument('--Z_momentum', default=0.9, type=float,
                          help='Momentum for normalization constant Z')
        
        # System configuration
        parser.add_argument('--use_cuda', default=True, type=ast.literal_eval,
                          help='Whether to use CUDA')
        parser.add_argument('--n_threads', default=8, type=int,
                          help='Number of data loading threads')
        parser.add_argument('--tracking', default=True, type=ast.literal_eval,
                          help='Whether to use tracking in BatchNorm')
        
        # Evaluation configuration
        parser.add_argument('--val_step', default=10, type=int,
                          help='Validation frequency (epochs)')
        parser.add_argument('--cal_vec_batch_size', default=20, type=int,
                          help='Batch size for calculating normal vector')
        parser.add_argument('--memory_bank_size', default=200, type=int,
                          help='Size of memory bank for normal vectors')
        parser.add_argument('--window_size', default=6, type=int,
                          help='Window size for score post-processing')
        
        # Resume training
        parser.add_argument('--resume_path', default='', type=str,
                          help='Path to checkpoint for resuming training')
        
        return parser


class DADTrainer:
    """Main trainer class for Driver Anomaly Detection."""
    
    def __init__(self, config: argparse.Namespace):
        """
        Initialize trainer with configuration.
        
        Args:
            config: Configuration namespace from argument parser
        """
        self.config = config
        self.device = torch.device('cuda' if config.use_cuda and torch.cuda.is_available() else 'cpu')
        
        # Initialize components
        self.model = None
        self.model_head = None
        self.nce_average = None
        self.criterion = None
        self.optimizer = None
        self.memory_bank = []
        
        # Logging
        self.batch_logger = None
        self.epoch_logger = None
        
        # Setup directories
        self._setup_directories()
        
        print(f"Initialized DAD Trainer")
        print(f"Device: {self.device}")
        print(f"Mode: {config.mode}")
        print(f"View: {config.view}")
        print(f"Model: {config.model_type}{config.model_depth}")
    
    def _setup_directories(self):
        """Create necessary directories."""
        for folder in [self.config.checkpoint_folder, self.config.log_folder, self.config.score_folder]:
            os.makedirs(folder, exist_ok=True)
    
    def _create_data_loaders(self) -> Tuple[Optional[DataLoader], Optional[DataLoader], Optional[DataLoader]]:
        """
        Create data loaders for training and validation.
        
        Returns:
            Tuple of (normal_train_loader, anomalous_train_loader, val_loader)
        """
        # Define spatial transforms
        if self.config.mode == 'train':
            spatial_transform = spatial_transforms.Compose([
                spatial_transforms.Scale(self.config.sample_size),
                spatial_transforms.RandomHorizontalFlip(),
                spatial_transforms.ToTensor(self.config.norm_value)
            ])
        else:
            spatial_transform = spatial_transforms.Compose([
                spatial_transforms.Scale(self.config.sample_size),
                spatial_transforms.ToTensor(self.config.norm_value)
            ])
        
        # Define temporal transforms
        temporal_transform = TemporalSequentialCrop(self.config.sample_duration, 1)
        
        if self.config.mode == 'train':
            # Training data loaders
            normal_dataset = DAD(
                root_path=self.config.root_path,
                subset='train',
                view=self.config.view,
                sample_duration=self.config.sample_duration,
                type='normal',
                spatial_transform=spatial_transform,
                temporal_transform=temporal_transform
            )
            
            anomalous_dataset = DAD(
                root_path=self.config.root_path,
                subset='train',
                view=self.config.view,
                sample_duration=self.config.sample_duration,
                type='anormal',
                spatial_transform=spatial_transform,
                temporal_transform=temporal_transform
            )
            
            normal_loader = DataLoader(
                normal_dataset,
                batch_size=self.config.n_train_batch_size,
                shuffle=True,
                num_workers=self.config.n_threads,
                pin_memory=True
            )
            
            anomalous_loader = DataLoader(
                anomalous_dataset,
                batch_size=self.config.a_train_batch_size,
                shuffle=True,
                num_workers=self.config.n_threads,
                pin_memory=True
            )
            
            return normal_loader, anomalous_loader, None
        
        else:
            # Validation data loader
            val_dataset = DAD(
                root_path=self.config.root_path,
                subset='validation',
                view=self.config.view,
                sample_duration=self.config.sample_duration,
                spatial_transform=spatial_transform,
                temporal_transform=temporal_transform
            )
            
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.config.val_batch_size,
                shuffle=False,
                num_workers=self.config.n_threads,
                pin_memory=True
            )
            
            return None, None, val_loader
    
    def _initialize_model(self):
        """Initialize model, NCE components, and optimizer."""
        print("Initializing model and components...")
        
        # Create model
        self.model = generate_model(self.config)
        
        # Create model head (simple linear layer for feature projection)
        self.model_head = torch.nn.Linear(self.config.feature_dim, self.config.feature_dim)
        
        if self.config.use_cuda:
            self.model = self.model.cuda()
            self.model_head = self.model_head.cuda()
        
        # Initialize NCE components
        len_neg = 1000  # This should be calculated from actual dataset size
        len_pos = 100   # This should be calculated from actual dataset size
        
        self.nce_average = NCEAverage(
            feature_dim=self.config.feature_dim,
            num_negative_samples=len_neg,
            num_positive_samples=len_pos,
            temperature=self.config.tau,
            momentum=self.config.Z_momentum
        )
        
        self.criterion = NCECriterion(num_negative_samples=len_neg)
        
        if self.config.use_cuda:
            self.nce_average = self.nce_average.cuda()
            self.criterion = self.criterion.cuda()
        
        # Initialize optimizer
        self.optimizer = torch.optim.SGD(
            list(self.model.parameters()) + list(self.model_head.parameters()),
            lr=self.config.learning_rate,
            momentum=self.config.momentum,
            weight_decay=self.config.weight_decay
        )
        
        print("Model and components initialized successfully!")
    
    def _initialize_logging(self):
        """Initialize logging components."""
        batch_log_path = os.path.join(self.config.log_folder, f'batch_{self.config.view}.log')
        epoch_log_path = os.path.join(self.config.log_folder, f'epoch_{self.config.view}.log')
        
        self.batch_logger = Logger(
            batch_log_path,
            ['epoch', 'batch', 'loss', 'prob', 'lr']
        )
        
        self.epoch_logger = Logger(
            epoch_log_path,
            ['epoch', 'loss', 'prob', 'lr']
        )

    def train_epoch(self, normal_loader: DataLoader, anomalous_loader: DataLoader, epoch: int) -> Tuple[float, float]:
        """
        Train for one epoch.

        Args:
            normal_loader: DataLoader for normal samples
            anomalous_loader: DataLoader for anomalous samples
            epoch: Current epoch number

        Returns:
            Tuple of (average_loss, average_probability)
        """
        self.model.train()
        self.model_head.train()

        losses = AverageMeter()
        prob_meter = AverageMeter()

        # Create iterators
        normal_iter = iter(normal_loader)
        anomalous_iter = iter(anomalous_loader)

        # Calculate number of batches
        num_batches = min(len(normal_loader), len(anomalous_loader))

        for batch_idx in range(num_batches):
            try:
                # Get normal and anomalous data
                normal_data, normal_indices = next(normal_iter)
                anomalous_data, anomalous_indices = next(anomalous_iter)

                if self.config.use_cuda:
                    normal_data = normal_data.cuda()
                    anomalous_data = anomalous_data.cuda()

                # Combine data
                combined_data = torch.cat([normal_data, anomalous_data], dim=0)

                # Forward pass through model
                unnormed_features, normed_features = self.model(combined_data)
                features = self.model_head(unnormed_features)

                # Split features
                normal_features = features[:self.config.n_train_batch_size]
                anomalous_features = features[self.config.n_train_batch_size:]

                # Compute NCE
                similarity_scores, avg_prob = self.nce_average(
                    normal_features, anomalous_features,
                    normal_indices, anomalous_indices,
                    normed_features[:self.config.n_train_batch_size]
                )

                # Compute loss
                loss = self.criterion(similarity_scores)

                # Backward pass
                self.optimizer.zero_grad()
                loss.backward()
                self.optimizer.step()

                # Update memory bank
                self._update_memory_bank(normal_data)

                # Update meters
                losses.update(loss.item())
                prob_meter.update(avg_prob)

                # Log batch results
                if batch_idx % 10 == 0:
                    self.batch_logger.log({
                        'epoch': epoch,
                        'batch': batch_idx,
                        'loss': loss.item(),
                        'prob': avg_prob,
                        'lr': self.optimizer.param_groups[0]['lr']
                    })

                    print(f'Epoch {epoch}, Batch {batch_idx}/{num_batches}: '
                          f'Loss={loss.item():.4f}, Prob={avg_prob:.4f}')

            except StopIteration:
                break

        # Log epoch results
        self.epoch_logger.log({
            'epoch': epoch,
            'loss': losses.avg,
            'prob': prob_meter.avg,
            'lr': self.optimizer.param_groups[0]['lr']
        })

        return losses.avg, prob_meter.avg

    def _update_memory_bank(self, normal_data: torch.Tensor):
        """Update memory bank with normal driving patterns."""
        self.model.eval()
        with torch.no_grad():
            _, normalized_features = self.model(normal_data)
            average_feature = torch.mean(normalized_features, dim=0, keepdim=True)

            if len(self.memory_bank) < self.config.memory_bank_size:
                self.memory_bank.append(average_feature.cpu())
            else:
                self.memory_bank.pop(0)
                self.memory_bank.append(average_feature.cpu())
        self.model.train()

    def validate(self, val_loader: DataLoader) -> Dict[str, float]:
        """
        Validate model performance.

        Args:
            val_loader: DataLoader for validation data

        Returns:
            Dictionary containing validation metrics
        """
        self.model.eval()

        # Compute normal driving template
        if self.memory_bank:
            normal_template = torch.mean(torch.cat(self.memory_bank, dim=0), dim=0, keepdim=True)
            normal_template = l2_normalize(normal_template)
            if self.config.use_cuda:
                normal_template = normal_template.cuda()
        else:
            print("Warning: Memory bank is empty, cannot validate")
            return {'accuracy': 0.0, 'auc': 0.0}

        scores = []
        labels = []

        with torch.no_grad():
            for batch_idx, (data, targets) in enumerate(val_loader):
                if self.config.use_cuda:
                    data = data.cuda()

                # Forward pass
                _, normalized_features = self.model(data)

                # Compute similarity scores
                similarities = torch.mm(normalized_features, normal_template.t()).squeeze()

                scores.extend(similarities.cpu().numpy())
                labels.extend(targets.numpy())

        # Convert to numpy arrays
        scores = np.array(scores)
        labels = np.array(labels)

        # Apply post-processing
        processed_scores = post_process_scores(scores, self.config.window_size)

        # Evaluate
        accuracy, threshold, auc_score = evaluate_scores(processed_scores, labels)

        print(f'Validation Results: Accuracy={accuracy:.2f}%, AUC={auc_score:.3f}, Threshold={threshold:.3f}')

        return {
            'accuracy': accuracy,
            'auc': auc_score,
            'threshold': threshold
        }

    def save_checkpoint(self, epoch: int, metrics: Dict[str, float]):
        """Save model checkpoint."""
        checkpoint_path = os.path.join(
            self.config.checkpoint_folder,
            f'checkpoint_epoch_{epoch}_{self.config.view}.pth'
        )

        torch.save({
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'model_head_state_dict': self.model_head.state_dict(),
            'nce_average_state_dict': self.nce_average.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'memory_bank': self.memory_bank,
            'config': self.config,
            'metrics': metrics
        }, checkpoint_path)

        print(f'Checkpoint saved: {checkpoint_path}')

    def load_checkpoint(self, checkpoint_path: str):
        """Load model checkpoint."""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model_head.load_state_dict(checkpoint['model_head_state_dict'])
        self.nce_average.load_state_dict(checkpoint['nce_average_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.memory_bank = checkpoint['memory_bank']

        print(f'Checkpoint loaded: {checkpoint_path}')
        return checkpoint['epoch'], checkpoint['metrics']

    def train(self):
        """Main training loop."""
        print("Starting training...")

        # Initialize components
        self._initialize_model()
        self._initialize_logging()

        # Create data loaders
        normal_loader, anomalous_loader, _ = self._create_data_loaders()

        if normal_loader is None or anomalous_loader is None:
            raise ValueError("Failed to create training data loaders")

        # Resume from checkpoint if specified
        start_epoch = 1
        best_accuracy = 0.0

        if self.config.resume_path:
            start_epoch, metrics = self.load_checkpoint(self.config.resume_path)
            best_accuracy = metrics.get('accuracy', 0.0)
            start_epoch += 1

        # Training loop
        for epoch in range(start_epoch, start_epoch + self.config.epochs):
            print(f"\nEpoch {epoch}/{start_epoch + self.config.epochs - 1}")

            # Train for one epoch
            avg_loss, avg_prob = self.train_epoch(normal_loader, anomalous_loader, epoch)

            print(f"Training - Loss: {avg_loss:.4f}, Prob: {avg_prob:.4f}")

            # Validate periodically
            if epoch % self.config.val_step == 0:
                print("Running validation...")
                _, _, val_loader = self._create_data_loaders()

                if val_loader is not None:
                    metrics = self.validate(val_loader)

                    # Save checkpoint if best model
                    if metrics['accuracy'] > best_accuracy:
                        best_accuracy = metrics['accuracy']
                        self.save_checkpoint(epoch, metrics)
                        print(f"New best model saved! Accuracy: {best_accuracy:.2f}%")

            # Adjust learning rate (simple step decay)
            if epoch % 50 == 0:
                new_lr = self.config.learning_rate * (0.1 ** (epoch // 50))
                adjust_learning_rate(self.optimizer, new_lr)
                print(f"Learning rate adjusted to: {new_lr}")

        print("Training completed!")

    def test(self):
        """Main testing loop."""
        print("Starting testing...")

        # Initialize model
        self._initialize_model()

        # Load checkpoint
        if self.config.resume_path:
            self.load_checkpoint(self.config.resume_path)
        else:
            print("Warning: No checkpoint specified for testing")

        # Create validation data loader
        _, _, val_loader = self._create_data_loaders()

        if val_loader is None:
            raise ValueError("Failed to create validation data loader")

        # Run evaluation
        metrics = self.validate(val_loader)

        print(f"\nFinal Test Results:")
        print(f"Accuracy: {metrics['accuracy']:.2f}%")
        print(f"AUC: {metrics['auc']:.3f}")
        print(f"Threshold: {metrics['threshold']:.3f}")

        return metrics


def main():
    """Main function."""
    # Parse arguments
    parser = DADConfig.create_parser()
    config = parser.parse_args()

    # Set up CUDA
    if config.use_cuda and torch.cuda.is_available():
        cudnn.benchmark = True
        print(f"Using CUDA device: {torch.cuda.get_device_name()}")
    else:
        print("Using CPU")

    # Create trainer
    trainer = DADTrainer(config)

    # Run training or testing
    if config.mode == 'train':
        trainer.train()
    elif config.mode == 'test':
        trainer.test()
    else:
        raise ValueError(f"Invalid mode: {config.mode}")


if __name__ == '__main__':
    main()
