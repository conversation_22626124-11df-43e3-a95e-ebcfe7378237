import torch
import torch.nn as nn


class NCEAverage(nn.Module):
    """
    Noise Contrastive Estimation (NCE) Average for Driver Anomaly Detection.
    
    This module computes similarity scores between normal and anomalous driving patterns
    using contrastive learning. It maintains a dynamic normalization constant Z and
    computes probabilities for the NCE loss function.
    
    The module performs:
    1. Similarity computation between normal vectors (positive pairs)
    2. Similarity computation between normal and anomalous vectors (negative pairs)
    3. Dynamic normalization constant updates
    4. Probability extraction for loss computation
    """
    
    def __init__(self, feature_dim, num_negative_samples, num_positive_samples, 
                 temperature, momentum=0.9, normalization_constant=-1):
        """
        Initialize NCE Average module.
        
        Args:
            feature_dim (int): Dimension of feature embeddings
            num_negative_samples (int): Number of negative (anomalous) samples
            num_positive_samples (int): Number of positive (normal) samples  
            temperature (float): Temperature parameter for softmax (tau)
            momentum (float): Momentum for updating normalization constant
            normalization_constant (float): Initial normalization constant Z (-1 for auto-init)
        """
        super(NCEAverage, self).__init__()
        
        # Store configuration
        self.num_negative_samples = num_negative_samples
        self.num_positive_samples = num_positive_samples
        self.feature_dim = feature_dim
        
        # Register learnable parameters as buffer
        # [normalization_constant, temperature, momentum]
        self.register_buffer('nce_params', torch.tensor([
            normalization_constant, temperature, momentum
        ]))
        
        print(f'[NCE Average] Initialized with:')
        print(f'  - Feature dim: {feature_dim}')
        print(f'  - Temperature (tau): {temperature}')
        print(f'  - Momentum: {momentum}')
        print(f'  - Normalization constant Z: {normalization_constant}')
    
    @property
    def normalization_constant(self):
        """Get current normalization constant Z."""
        return self.nce_params[0].item()
    
    @property
    def temperature(self):
        """Get temperature parameter tau."""
        return self.nce_params[1].item()
    
    @property
    def momentum(self):
        """Get momentum parameter."""
        return self.nce_params[2].item()
    
    def compute_similarity_scores(self, positive_logits, negative_logits):
        """
        Compute normalized similarity scores for NCE.
        
        Args:
            positive_logits (torch.Tensor): Similarity scores for positive pairs
            negative_logits (torch.Tensor): Similarity scores for negative pairs
            
        Returns:
            tuple: (normalized_scores, average_positive_probability)
        """
        # Combine positive and negative logits
        all_logits = torch.cat([positive_logits, negative_logits], dim=-1)
        
        # Apply temperature scaling
        scaled_scores = torch.exp(all_logits / self.temperature)
        
        # Update normalization constant Z
        current_z = self.normalization_constant
        if current_z < 0:
            # Initialize Z with first batch statistics
            new_z = scaled_scores.mean() * self.num_negative_samples
            self.nce_params[0] = new_z
            print(f'Normalization constant Z initialized to: {new_z:.1f}')
        else:
            # Update Z with momentum
            batch_z = scaled_scores.mean() * self.num_negative_samples
            updated_z = (1 - self.momentum) * batch_z + self.momentum * current_z
            self.nce_params[0] = updated_z
        
        # Normalize scores
        normalized_scores = scaled_scores / self.nce_params[0]
        
        # Extract probability statistics
        avg_positive_prob = self._extract_positive_probability(normalized_scores)
        
        return normalized_scores, avg_positive_prob
    
    def _extract_positive_probability(self, scores):
        """
        Extract average probability of positive pairs.
        
        Args:
            scores (torch.Tensor): Normalized similarity scores
            
        Returns:
            float: Average probability of positive pairs
        """
        # Compute probabilities (softmax over each row)
        probabilities = scores / torch.sum(scores, dim=1, keepdim=True)
        
        # Return average probability of first column (positive pairs)
        return probabilities[:, 0].mean().item()
    
    def forward(self, normal_embeddings, anomalous_embeddings, 
                normal_indices=None, anomalous_indices=None, normalized_embeddings=None):
        """
        Forward pass to compute NCE similarity scores.
        
        Args:
            normal_embeddings (torch.Tensor): Normal driving embeddings
            anomalous_embeddings (torch.Tensor): Anomalous driving embeddings
            normal_indices (torch.Tensor): Indices for normal samples (unused)
            anomalous_indices (torch.Tensor): Indices for anomalous samples (unused)
            normalized_embeddings (torch.Tensor): Pre-normalized embeddings (unused)
            
        Returns:
            tuple: (similarity_scores, average_positive_probability)
        """
        batch_size = normal_embeddings.size(0)
        
        # Compute positive similarities (normal-normal pairs)
        normal_similarity_matrix = torch.mm(normal_embeddings, normal_embeddings.t())
        
        # Extract off-diagonal elements (exclude self-similarity)
        mask = ~torch.eye(batch_size, dtype=torch.bool, device=normal_embeddings.device)
        positive_logits = normal_similarity_matrix[mask].view(batch_size, -1).view(-1, 1)
        
        # Compute negative similarities (normal-anomalous pairs)
        normal_anomalous_scores = torch.mm(normal_embeddings, anomalous_embeddings.t())
        
        # Repeat to match positive logits shape
        num_positive_pairs = batch_size - 1
        negative_logits = normal_anomalous_scores.repeat(1, num_positive_pairs).view(
            positive_logits.size(0), -1
        )
        
        # Compute final similarity scores
        similarity_scores, avg_positive_prob = self.compute_similarity_scores(
            positive_logits, negative_logits
        )
        
        return similarity_scores, avg_positive_prob
    
    def get_info(self):
        """
        Get information about the NCE Average configuration.
        
        Returns:
            dict: Configuration and current state information
        """
        return {
            'feature_dim': self.feature_dim,
            'num_negative_samples': self.num_negative_samples,
            'num_positive_samples': self.num_positive_samples,
            'temperature': self.temperature,
            'momentum': self.momentum,
            'current_normalization_constant': self.normalization_constant,
            'module_type': 'NCE Average'
        }


def test_nce_average():
    """
    Test function to demonstrate NCE Average usage.
    """
    print("Testing NCE Average...")
    
    # Test parameters
    feature_dim = 128
    num_negative_samples = 1000
    num_positive_samples = 50
    temperature = 0.1
    batch_size = 4
    anomalous_batch_size = 10
    
    # Create NCE Average module
    nce_average = NCEAverage(
        feature_dim=feature_dim,
        num_negative_samples=num_negative_samples,
        num_positive_samples=num_positive_samples,
        temperature=temperature
    )
    
    # Create dummy embeddings
    normal_embeddings = torch.randn(batch_size, feature_dim)
    anomalous_embeddings = torch.randn(anomalous_batch_size, feature_dim)
    
    # Normalize embeddings (L2 normalization)
    normal_embeddings = normal_embeddings / torch.norm(normal_embeddings, dim=1, keepdim=True)
    anomalous_embeddings = anomalous_embeddings / torch.norm(anomalous_embeddings, dim=1, keepdim=True)
    
    # Forward pass
    similarity_scores, avg_positive_prob = nce_average(normal_embeddings, anomalous_embeddings)
    
    print(f"Normal embeddings shape: {normal_embeddings.shape}")
    print(f"Anomalous embeddings shape: {anomalous_embeddings.shape}")
    print(f"Similarity scores shape: {similarity_scores.shape}")
    print(f"Average positive probability: {avg_positive_prob:.4f}")
    print(f"NCE Average info: {nce_average.get_info()}")
    
    return similarity_scores, avg_positive_prob


if __name__ == '__main__':
    test_nce_average()
