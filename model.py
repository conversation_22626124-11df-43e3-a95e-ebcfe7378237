import torch
from torch import nn
from models import shufflenet, shufflenetv2, resnet, mobilenet, mobilenetv2
from utils import _construct_depth_model


class ModelFactory:
    """
    Factory class for creating different model architectures for Driver Anomaly Detection.

    Supports ResNet, ShuffleNet, ShuffleNetV2, MobileNet, and MobileNetV2 architectures
    with optional pre-training from Kinetics dataset.
    """

    # Supported model types and their configurations
    SUPPORTED_MODELS = ['resnet', 'shufflenet', 'shufflenetv2', 'mobilenet', 'mobilenetv2']
    RESNET_DEPTHS = [18, 50, 101]

    # Pre-trained model path templates
    PRETRAINED_PATHS = {
        'resnet': './premodels/kinetics_resnet_{depth}_RGB_16_best.pth',
        'shufflenet': './premodels/kinetics_shufflenet_{width_mult}x_G3_RGB_16_best.pth',
        'shufflenetv2': './premodels/kinetics_shufflenetv2_{width_mult}x_RGB_16_best.pth',
        'mobilenet': './premodels/kinetics_mobilenet_{width_mult}x_RGB_16_best.pth',
        'mobilenetv2': './premodels/kinetics_mobilenetv2_{width_mult}x_RGB_16_best.pth'
    }

    @staticmethod
    def _create_resnet_model(args, use_pretrained_shortcut=False):
        """Create ResNet model based on depth."""
        assert args.model_depth in ModelFactory.RESNET_DEPTHS, \
            f"ResNet depth must be one of {ModelFactory.RESNET_DEPTHS}"

        # Determine shortcut type
        if use_pretrained_shortcut:
            shortcut_type = 'A' if args.model_depth == 18 else 'B'
        else:
            shortcut_type = args.shortcut_type

        # Common parameters for all ResNet variants
        common_params = {
            'output_dim': args.feature_dim,
            'sample_size': args.sample_size,
            'sample_duration': args.sample_duration,
            'shortcut_type': shortcut_type,
            'tracking': args.tracking,
            'pre_train': args.pre_train_model
        }

        # Create model based on depth
        if args.model_depth == 18:
            return resnet.resnet18(**common_params)
        elif args.model_depth == 50:
            return resnet.resnet50(**common_params)
        elif args.model_depth == 101:
            return resnet.resnet101(**common_params)

    @staticmethod
    def _create_shufflenet_model(args):
        """Create ShuffleNet model."""
        return shufflenet.get_model(
            groups=args.groups,
            width_mult=args.width_mult,
            output_dim=args.feature_dim,
            pre_train=args.pre_train_model
        )

    @staticmethod
    def _create_shufflenetv2_model(args):
        """Create ShuffleNetV2 model."""
        return shufflenetv2.get_model(
            output_dim=args.feature_dim,
            sample_size=args.sample_size,
            width_mult=args.width_mult,
            pre_train=args.pre_train_model
        )

    @staticmethod
    def _create_mobilenet_model(args):
        """Create MobileNet model."""
        return mobilenet.get_model(
            sample_size=args.sample_size,
            width_mult=args.width_mult,
            pre_train=args.pre_train_model
        )

    @staticmethod
    def _create_mobilenetv2_model(args):
        """Create MobileNetV2 model."""
        return mobilenetv2.get_model(
            sample_size=args.sample_size,
            width_mult=args.width_mult,
            pre_train=args.pre_train_model
        )

    @staticmethod
    def create_model_without_pretraining(args):
        """Create model without pre-trained weights."""
        print('Creating model without pre-trained weights')

        if args.model_type == 'resnet':
            model = ModelFactory._create_resnet_model(args)
        elif args.model_type == 'shufflenet':
            model = ModelFactory._create_shufflenet_model(args)
        elif args.model_type == 'shufflenetv2':
            model = ModelFactory._create_shufflenetv2_model(args)
        elif args.model_type == 'mobilenet':
            model = ModelFactory._create_mobilenet_model(args)
        elif args.model_type == 'mobilenetv2':
            model = ModelFactory._create_mobilenetv2_model(args)

        return nn.DataParallel(model, device_ids=None)
    @staticmethod
    def create_model_with_pretraining(args):
        """Create model with pre-trained weights from Kinetics dataset."""
        print('Creating model with pre-trained weights from Kinetics dataset')

        # Get pre-trained model path
        if args.model_type == 'resnet':
            pre_model_path = ModelFactory.PRETRAINED_PATHS['resnet'].format(depth=args.model_depth)
            model = ModelFactory._create_resnet_model(args, use_pretrained_shortcut=True)
        elif args.model_type == 'shufflenet':
            pre_model_path = ModelFactory.PRETRAINED_PATHS['shufflenet'].format(width_mult=args.width_mult)
            model = ModelFactory._create_shufflenet_model(args)
        elif args.model_type == 'shufflenetv2':
            pre_model_path = ModelFactory.PRETRAINED_PATHS['shufflenetv2'].format(width_mult=args.width_mult)
            model = ModelFactory._create_shufflenetv2_model(args)
        elif args.model_type == 'mobilenet':
            pre_model_path = ModelFactory.PRETRAINED_PATHS['mobilenet'].format(width_mult=args.width_mult)
            model = ModelFactory._create_mobilenet_model(args)
        elif args.model_type == 'mobilenetv2':
            pre_model_path = ModelFactory.PRETRAINED_PATHS['mobilenetv2'].format(width_mult=args.width_mult)
            model = ModelFactory._create_mobilenetv2_model(args)

        # Wrap in DataParallel for loading pre-trained weights
        model = nn.DataParallel(model, device_ids=None)

        # Load and apply pre-trained weights
        ModelFactory._load_pretrained_weights(model, pre_model_path)

        # Apply depth model construction if needed
        model = _construct_depth_model(model)

        return model

    @staticmethod
    def _load_pretrained_weights(model, pretrained_path):
        """Load pre-trained weights into model."""
        try:
            model_dict = model.state_dict()
            pretrained_dict = torch.load(pretrained_path)['state_dict']

            # Filter out incompatible keys
            pretrained_dict = {k: v for k, v in pretrained_dict.items() if k in model_dict}

            print(f'Loading {len(pretrained_dict)} pre-trained parameters from {pretrained_path}')

            # Update model dictionary and load state
            model_dict.update(pretrained_dict)
            model.load_state_dict(model_dict)

        except FileNotFoundError:
            print(f'Warning: Pre-trained model file not found: {pretrained_path}')
            print('Continuing without pre-trained weights...')
        except Exception as e:
            print(f'Warning: Error loading pre-trained weights: {e}')
            print('Continuing without pre-trained weights...')


def generate_model(args):
    """
    Generate model for Driver Anomaly Detection.

    Args:
        args: Configuration arguments containing model specifications

    Returns:
        torch.nn.Module: Configured model ready for training or inference
    """
    # Validate model type
    assert args.model_type in ModelFactory.SUPPORTED_MODELS, \
        f"Model type must be one of {ModelFactory.SUPPORTED_MODELS}"

    # Create model based on pre-training preference
    if not args.pre_train_model or args.mode == 'test':
        model = ModelFactory.create_model_without_pretraining(args)
    else:
        model = ModelFactory.create_model_with_pretraining(args)

    # Move to GPU if requested
    if args.use_cuda:
        model = model.cuda()
        print(f'Model moved to CUDA device')

    return model


def test_model_generation():
    """
    Test function to demonstrate model generation.
    """
    print("Testing Model Generation...")

    # Create a mock args object for testing
    class MockArgs:
        def __init__(self):
            self.model_type = 'resnet'
            self.model_depth = 18
            self.feature_dim = 128
            self.sample_size = 112
            self.sample_duration = 16
            self.shortcut_type = 'A'
            self.tracking = False
            self.pre_train_model = False
            self.mode = 'train'
            self.use_cuda = False
            # Additional attributes for other models
            self.groups = 3
            self.width_mult = 1.0

    args = MockArgs()

    # Test ResNet model creation
    print(f"Creating {args.model_type}{args.model_depth} model...")
    model = generate_model(args)
    print(f"Model created successfully!")
    print(f"Model type: {type(model)}")

    # Test model factory info
    print(f"Supported models: {ModelFactory.SUPPORTED_MODELS}")
    print(f"ResNet depths: {ModelFactory.RESNET_DEPTHS}")

    return model


if __name__ == '__main__':
    test_model_generation()
