import torch
import torch.nn as nn


class NCECriterion(nn.Module):
    """
    Noise Contrastive Estimation (NCE) Loss for Driver Anomaly Detection.

    This loss function implements contrastive learning to distinguish between:
    - Normal driving patterns (positive samples)
    - Anomalous driving patterns (negative samples)

    The goal is to learn a representation where normal driving behaviors cluster
    together, enabling anomaly detection by measuring deviation from this cluster.
    """

    def __init__(self, num_negative_samples):
        """
        Initialize NCE Criterion.

        Args:
            num_negative_samples (int): Total number of negative samples in dataset
        """
        super(NCECriterion, self).__init__()
        self.num_negative_samples = num_negative_samples
        self.eps = 1e-7  # Small constant to prevent log(0)

    def forward(self, similarity_scores):
        """
        Compute NCE loss from similarity scores.

        Args:
            similarity_scores (torch.Tensor): Shape (batch_size, k+1)
                - First column: positive pair similarities (normal-normal)
                - Remaining k columns: negative pair similarities (normal-anomalous)

        Returns:
            torch.Tensor: NCE loss value
        """
        batch_size, total_samples = similarity_scores.shape
        num_negatives = total_samples - 1

        # Uniform noise distribution assumption
        noise_prob = 1.0 / self.num_negative_samples

        # Extract positive and negative similarities
        positive_scores = similarity_scores[:, 0]  # Shape: (batch_size,)
        negative_scores = similarity_scores[:, 1:]  # Shape: (batch_size, k)

        # Compute positive pair loss: log P(positive_pair | data)
        positive_denominator = positive_scores + num_negatives * noise_prob + self.eps
        positive_loss = torch.log(positive_scores / positive_denominator)

        # Compute negative pair loss: log P(negative_pair | noise)
        negative_numerator = num_negatives * noise_prob
        negative_denominator = negative_scores + num_negatives * noise_prob + self.eps
        negative_loss = torch.log(negative_numerator / negative_denominator)

        # Combine losses and normalize by batch size
        total_positive_loss = positive_loss.sum()
        total_negative_loss = negative_loss.sum()

        # NCE loss (negative log-likelihood)
        nce_loss = -(total_positive_loss + total_negative_loss) / batch_size

        return nce_loss

    def get_info(self):
        """
        Get information about the NCE criterion configuration.

        Returns:
            dict: Configuration information
        """
        return {
            'num_negative_samples': self.num_negative_samples,
            'epsilon': self.eps,
            'criterion_type': 'NCE (Noise Contrastive Estimation)'
        }


def test_nce_criterion():
    """
    Test function to demonstrate NCE Criterion usage.
    Creates dummy similarity scores and computes NCE loss.
    """
    print("Testing NCE Criterion...")

    # Test parameters
    batch_size = 4
    num_negatives = 10
    num_negative_samples = 9000

    # Create NCE criterion
    criterion = NCECriterion(num_negative_samples)

    # Create dummy similarity scores
    # Shape: (batch_size, num_negatives + 1)
    # First column: positive similarities (should be higher)
    # Remaining columns: negative similarities (should be lower)
    similarity_scores = torch.randn(batch_size, num_negatives + 1)

    # Make positive scores higher than negative scores for realistic test
    similarity_scores[:, 0] = torch.abs(similarity_scores[:, 0]) + 1.0  # Positive scores
    similarity_scores[:, 1:] = torch.abs(similarity_scores[:, 1:]) * 0.5  # Negative scores

    # Compute loss
    loss = criterion(similarity_scores)

    print(f"Input shape: {similarity_scores.shape}")
    print(f"Positive scores (first column): {similarity_scores[:, 0]}")
    print(f"Negative scores (mean): {similarity_scores[:, 1:].mean()}")
    print(f"NCE Loss: {loss.item():.4f}")

    return loss


if __name__ == '__main__':
    test_nce_criterion()
