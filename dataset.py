"""
Dataset module for Driver Anomaly Detection (DAD).

This module provides optimized dataset loading and preprocessing for:
- Normal and anomalous driving behavior videos
- Multiple camera views (front/top) and modalities (depth/IR)
- Training and validation data handling
- Efficient video clip sampling and loading
"""

import csv
import os
from typing import List, Dict, Any, Optional, Callable, Tuple

import torch
import torch.utils.data as data
from PIL import Image


# ============================================================================
# IMAGE LOADING UTILITIES
# ============================================================================

def load_image_pil(image_path: str) -> Image.Image:
    """
    Load image using PIL with proper resource management.
    
    Args:
        image_path (str): Path to image file
        
    Returns:
        PIL.Image: Loaded image in grayscale format
    """
    try:
        with open(image_path, 'rb') as f:
            with Image.open(f) as img:
                return img.convert('L')  # Convert to grayscale
    except Exception as e:
        raise IOError(f"Failed to load image {image_path}: {e}")


def load_image_accimage(image_path: str) -> Image.Image:
    """
    Load image using accimage (faster alternative to PIL).
    
    Args:
        image_path (str): Path to image file
        
    Returns:
        Image: Loaded image
    """
    try:
        import accimage
        return accimage.Image(image_path)
    except (ImportError, IOError):
        # Fall back to PIL if accimage is not available or fails
        return load_image_pil(image_path)


def get_image_loader() -> Callable[[str], Image.Image]:
    """
    Get the best available image loader.
    
    Returns:
        Callable: Image loading function
    """
    try:
        from torchvision import get_image_backend
        if get_image_backend() == 'accimage':
            return load_image_accimage
    except ImportError:
        pass
    
    return load_image_pil


# ============================================================================
# VIDEO PROCESSING UTILITIES
# ============================================================================

class VideoClipLoader:
    """Utility class for loading video clips from frame sequences."""
    
    def __init__(self, image_loader: Optional[Callable] = None):
        """
        Initialize video clip loader.
        
        Args:
            image_loader: Function to load individual images
        """
        self.image_loader = image_loader or get_image_loader()
    
    def load_clip(self, video_path: str, frame_indices: List[int]) -> List[Image.Image]:
        """
        Load video clip as sequence of images.
        
        Args:
            video_path (str): Path to directory containing video frames
            frame_indices (List[int]): List of frame indices to load
            
        Returns:
            List[PIL.Image]: List of loaded frames
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video path does not exist: {video_path}")
        
        frames = []
        for frame_idx in frame_indices:
            # Frame naming convention: img_{index}.png
            frame_name = f"img_{frame_idx}.png"
            frame_path = os.path.join(video_path, frame_name)
            
            if not os.path.exists(frame_path):
                raise FileNotFoundError(f"Frame not found: {frame_path}")
            
            frame = self.image_loader(frame_path)
            frames.append(frame)
        
        return frames


def create_video_clips(video_path: str, start_frame: int, end_frame: int, 
                      label: int, view: str, clip_duration: int) -> List[Dict[str, Any]]:
    """
    Create video clips from a video segment.
    
    Args:
        video_path (str): Path to video directory
        start_frame (int): Starting frame index
        end_frame (int): Ending frame index
        label (int): Ground truth label (1=normal, 0=anomalous)
        view (str): Camera view identifier
        clip_duration (int): Number of frames per clip
        
    Returns:
        List[Dict]: List of clip metadata dictionaries
    """
    clips = []
    
    # Calculate number of clips needed
    total_frames = end_frame - start_frame + 1
    
    if total_frames <= clip_duration:
        # Single clip with padding if necessary
        frame_indices = list(range(start_frame, end_frame + 1))
        
        # Pad with last frame if needed
        if len(frame_indices) < clip_duration:
            padding_needed = clip_duration - len(frame_indices)
            frame_indices.extend([end_frame] * padding_needed)
        
        clip_info = {
            'video': video_path,
            'label': label,
            'subset': 'validation',
            'view': view,
            'frame_indices': frame_indices,
            'action': 'normal' if label == 1 else 'anomalous'
        }
        clips.append(clip_info)
    
    else:
        # Multiple clips with sliding window
        for start_idx in range(start_frame, end_frame - clip_duration + 2, clip_duration):
            end_idx = min(start_idx + clip_duration - 1, end_frame)
            frame_indices = list(range(start_idx, end_idx + 1))
            
            # Pad if necessary
            if len(frame_indices) < clip_duration:
                padding_needed = clip_duration - len(frame_indices)
                frame_indices.extend([end_idx] * padding_needed)
            
            clip_info = {
                'video': video_path,
                'label': label,
                'subset': 'validation',
                'view': view,
                'frame_indices': frame_indices,
                'action': 'normal' if label == 1 else 'anomalous'
            }
            clips.append(clip_info)
    
    return clips


# ============================================================================
# DATASET CREATION
# ============================================================================

class DADDatasetBuilder:
    """Builder class for creating DAD datasets."""
    
    @staticmethod
    def build_training_dataset(root_path: str, view: str, clip_duration: int, 
                             data_type: str) -> List[Dict[str, Any]]:
        """
        Build training dataset for normal or anomalous samples.
        
        Args:
            root_path (str): Root path to dataset
            view (str): Camera view (front_depth, front_IR, top_depth, top_IR)
            clip_duration (int): Number of frames per clip
            data_type (str): 'normal' or 'anormal'
            
        Returns:
            List[Dict]: List of sample metadata
        """
        dataset = []
        
        # Construct path to training data
        train_path = os.path.join(root_path, 'train', data_type)
        
        if not os.path.exists(train_path):
            print(f"Warning: Training path does not exist: {train_path}")
            return dataset
        
        try:
            # Iterate through training videos
            for video_folder in os.listdir(train_path):
                video_path = os.path.join(train_path, video_folder, view)
                
                if not os.path.exists(video_path):
                    continue
                
                # Get all frame files
                frame_files = [f for f in os.listdir(video_path) if f.startswith('img_') and f.endswith('.png')]
                
                if not frame_files:
                    continue
                
                # Extract frame indices and sort
                frame_indices = []
                for frame_file in frame_files:
                    try:
                        frame_idx = int(frame_file.split('_')[1].split('.')[0])
                        frame_indices.append(frame_idx)
                    except (ValueError, IndexError):
                        continue
                
                frame_indices.sort()
                
                if len(frame_indices) < clip_duration:
                    continue
                
                # Create clips with sliding window
                for start_idx in range(0, len(frame_indices) - clip_duration + 1, clip_duration):
                    clip_indices = frame_indices[start_idx:start_idx + clip_duration]
                    
                    sample_info = {
                        'video': video_path,
                        'label': 1 if data_type == 'normal' else 0,
                        'subset': 'train',
                        'view': view,
                        'frame_indices': clip_indices,
                        'action': data_type
                    }
                    dataset.append(sample_info)
        
        except Exception as e:
            print(f"Error building training dataset: {e}")
        
        return dataset
    
    @staticmethod
    def build_validation_dataset(root_path: str, view: str, clip_duration: int) -> List[Dict[str, Any]]:
        """
        Build validation dataset from label CSV file.
        
        Args:
            root_path (str): Root path to dataset
            view (str): Camera view
            clip_duration (int): Number of frames per clip
            
        Returns:
            List[Dict]: List of validation samples
        """
        dataset = []
        csv_path = os.path.join(root_path, 'LABEL.csv')
        
        if not os.path.exists(csv_path):
            print(f"Warning: Label file not found: {csv_path}")
            return dataset
        
        try:
            with open(csv_path, 'r') as csv_file:
                csv_reader = csv.reader(csv_file, delimiter=',')
                current_val_path = None
                
                for row in csv_reader:
                    if not row or row[-1] == '':
                        continue
                    
                    # Update validation path when new video starts
                    if row[0] != '':
                        current_val_path = os.path.join(root_path, row[0].strip())
                    
                    if len(row) >= 5 and row[1] != '':
                        video_path = os.path.join(current_val_path, row[1], view)
                        
                        if not os.path.exists(video_path):
                            continue
                        
                        start_frame = int(row[2])
                        end_frame = int(row[3])
                        label = 1 if row[4] == 'N' else 0
                        
                        # Create clips for this segment
                        clips = create_video_clips(
                            video_path, start_frame, end_frame, 
                            label, view, clip_duration
                        )
                        dataset.extend(clips)
        
        except Exception as e:
            print(f"Error building validation dataset: {e}")
        
        return dataset


def create_dataset(root_path: str, subset: str, view: str, 
                  clip_duration: int, data_type: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Create dataset based on specified parameters.
    
    Args:
        root_path (str): Root path to dataset
        subset (str): 'train' or 'validation'
        view (str): Camera view
        clip_duration (int): Number of frames per clip
        data_type (Optional[str]): For training: 'normal' or 'anormal'
        
    Returns:
        List[Dict]: Dataset samples
    """
    builder = DADDatasetBuilder()
    
    if subset == 'train' and data_type in ['normal', 'anormal']:
        return builder.build_training_dataset(root_path, view, clip_duration, data_type)
    elif subset == 'validation' and data_type is None:
        return builder.build_validation_dataset(root_path, view, clip_duration)
    else:
        print(f"Invalid dataset configuration: subset={subset}, data_type={data_type}")
        return []


# ============================================================================
# PYTORCH DATASET CLASS
# ============================================================================

class DAD(data.Dataset):
    """
    PyTorch Dataset class for Driver Anomaly Detection.

    Supports both training and validation modes with efficient video clip loading,
    spatial and temporal transformations, and proper data formatting for PyTorch.
    """

    def __init__(self, root_path: str, subset: str, view: str,
                 sample_duration: int = 16, data_type: Optional[str] = None,
                 video_loader: Optional[Callable] = None,
                 spatial_transform: Optional[Callable] = None,
                 temporal_transform: Optional[Callable] = None):
        """
        Initialize DAD dataset.

        Args:
            root_path (str): Root path to dataset
            subset (str): 'train' or 'validation'
            view (str): Camera view (front_depth, front_IR, top_depth, top_IR)
            sample_duration (int): Number of frames per video clip
            data_type (Optional[str]): For training: 'normal' or 'anormal'
            video_loader (Optional[Callable]): Custom video loading function
            spatial_transform (Optional[Callable]): Spatial transformations
            temporal_transform (Optional[Callable]): Temporal transformations
        """
        # Store configuration
        self.root_path = root_path
        self.subset = subset
        self.view = view
        self.sample_duration = sample_duration
        self.data_type = data_type

        # Initialize video loader
        if video_loader is None:
            self.video_loader = VideoClipLoader()
        else:
            self.video_loader = video_loader

        # Store transforms
        self.spatial_transform = spatial_transform
        self.temporal_transform = temporal_transform

        # Load dataset
        self.samples = create_dataset(root_path, subset, view, sample_duration, data_type)

        print(f"DAD Dataset initialized:")
        print(f"  - Subset: {subset}")
        print(f"  - View: {view}")
        print(f"  - Data type: {data_type}")
        print(f"  - Sample duration: {sample_duration}")
        print(f"  - Number of samples: {len(self.samples)}")

    def __len__(self) -> int:
        """Return number of samples in dataset."""
        return len(self.samples)

    def __getitem__(self, index: int) -> Tuple[torch.Tensor, Any]:
        """
        Get dataset item by index.

        Args:
            index (int): Sample index

        Returns:
            Tuple: (video_tensor, target) where target depends on subset
        """
        if index >= len(self.samples):
            raise IndexError(f"Index {index} out of range for dataset of size {len(self.samples)}")

        sample = self.samples[index]

        try:
            # Load video clip
            video_clip = self._load_video_clip(sample)

            # Apply transforms and convert to tensor
            video_tensor = self._process_video_clip(video_clip)

            # Return appropriate target based on subset
            if self.subset == 'train':
                return video_tensor, index
            elif self.subset == 'validation':
                return video_tensor, sample['label']
            else:
                raise ValueError(f"Unknown subset: {self.subset}")

        except Exception as e:
            print(f"Error loading sample {index}: {e}")
            # Return dummy data to prevent training interruption
            dummy_tensor = torch.zeros(1, self.sample_duration, 112, 112)
            dummy_target = 0 if self.subset == 'validation' else index
            return dummy_tensor, dummy_target

    def _load_video_clip(self, sample: Dict[str, Any]) -> List[Image.Image]:
        """
        Load video clip from sample metadata.

        Args:
            sample (Dict): Sample metadata

        Returns:
            List[PIL.Image]: Loaded video frames
        """
        video_path = sample['video']
        frame_indices = sample['frame_indices']

        # Apply temporal transform if available
        if self.temporal_transform is not None:
            frame_indices = self.temporal_transform(frame_indices)

        # Load video clip
        if hasattr(self.video_loader, 'load_clip'):
            return self.video_loader.load_clip(video_path, frame_indices)
        else:
            # Fallback for custom loaders
            return self.video_loader(video_path, frame_indices)

    def _process_video_clip(self, video_clip: List[Image.Image]) -> torch.Tensor:
        """
        Process video clip with spatial transforms and convert to tensor.

        Args:
            video_clip (List[PIL.Image]): Raw video frames

        Returns:
            torch.Tensor: Processed video tensor with shape (C, T, H, W)
        """
        # Apply spatial transforms
        if self.spatial_transform is not None:
            # Randomize parameters for data augmentation
            if hasattr(self.spatial_transform, 'randomize_parameters'):
                self.spatial_transform.randomize_parameters()

            # Apply transform to each frame
            processed_frames = [self.spatial_transform(frame) for frame in video_clip]
        else:
            # Convert to tensor without transforms
            import torchvision.transforms as transforms
            to_tensor = transforms.ToTensor()
            processed_frames = [to_tensor(frame) for frame in video_clip]

        # Stack frames and rearrange dimensions
        # From (T, C, H, W) to (C, T, H, W)
        video_tensor = torch.stack(processed_frames, dim=0).permute(1, 0, 2, 3)

        return video_tensor

    def get_sample_info(self, index: int) -> Dict[str, Any]:
        """
        Get metadata for a specific sample.

        Args:
            index (int): Sample index

        Returns:
            Dict: Sample metadata
        """
        if index >= len(self.samples):
            raise IndexError(f"Index {index} out of range")

        return self.samples[index].copy()

    def get_dataset_stats(self) -> Dict[str, Any]:
        """
        Get dataset statistics.

        Returns:
            Dict: Dataset statistics
        """
        if not self.samples:
            return {'total_samples': 0}

        stats = {
            'total_samples': len(self.samples),
            'subset': self.subset,
            'view': self.view,
            'sample_duration': self.sample_duration
        }

        if self.subset == 'validation':
            # Count normal vs anomalous samples
            normal_count = sum(1 for sample in self.samples if sample['label'] == 1)
            anomalous_count = len(self.samples) - normal_count

            stats.update({
                'normal_samples': normal_count,
                'anomalous_samples': anomalous_count,
                'normal_ratio': normal_count / len(self.samples) if self.samples else 0
            })

        return stats


# ============================================================================
# BACKWARD COMPATIBILITY
# ============================================================================

# Legacy function names for backward compatibility
get_video = lambda video_path, frame_indices: VideoClipLoader().load_clip(video_path, frame_indices)
get_clips = create_video_clips
make_dataset = create_dataset


# ============================================================================
# TESTING
# ============================================================================

def test_dataset():
    """Test dataset functionality."""
    print("Testing DAD Dataset...")

    # This is a basic test that doesn't require actual data
    print("Dataset classes and functions loaded successfully!")

    # Test VideoClipLoader
    loader = VideoClipLoader()
    print(f"VideoClipLoader initialized: {type(loader)}")

    # Test dataset builder
    builder = DADDatasetBuilder()
    print(f"DADDatasetBuilder initialized: {type(builder)}")

    print("Dataset test completed!")


if __name__ == '__main__':
    test_dataset()
