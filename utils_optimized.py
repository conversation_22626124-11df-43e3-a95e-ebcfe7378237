"""
Utility functions for Driver Anomaly Detection.

This module contains various utility functions for:
- Data preprocessing and normalization
- Model evaluation and metrics
- Logging and monitoring
- Score processing and fusion
"""

import csv
import os
from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import torch
from torch import nn

# Optional imports with fallbacks
try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    print("Warning: matplotlib not available. Plotting functions will be disabled.")

try:
    from sklearn import metrics
    from sklearn.metrics import auc
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    print("Warning: sklearn not available. Some evaluation functions will be disabled.")


# ============================================================================
# TENSOR OPERATIONS
# ============================================================================

def l2_normalize(x: torch.Tensor, dim: int = 1, eps: float = 1e-8) -> torch.Tensor:
    """
    L2 normalize tensor along specified dimension.
    
    Args:
        x (torch.Tensor): Input tensor
        dim (int): Dimension to normalize along
        eps (float): Small value to prevent division by zero
        
    Returns:
        torch.Tensor: L2 normalized tensor
    """
    norm = torch.sqrt(torch.sum(x**2, dim=dim, keepdim=True) + eps)
    return x / norm


def adjust_learning_rate(optimizer: torch.optim.Optimizer, learning_rate: float) -> None:
    """
    Adjust learning rate for all parameter groups in optimizer.
    
    Args:
        optimizer (torch.optim.Optimizer): PyTorch optimizer
        learning_rate (float): New learning rate
    """
    for param_group in optimizer.param_groups:
        param_group['lr'] = learning_rate


# ============================================================================
# LOGGING AND MONITORING
# ============================================================================

class Logger:
    """
    CSV logger for training process with resume capability.
    
    Supports logging training metrics to CSV files with automatic header
    management and resume functionality.
    """
    
    def __init__(self, log_path: str, header: List[str], resume: bool = False):
        """
        Initialize logger.
        
        Args:
            log_path (str): Path to log file
            header (List[str]): List of column names for logging
            resume (bool): Whether to resume from existing log file
        """
        self.log_path = log_path
        self.header = header
        self.resume = resume
        self.log_file = None
        self.logger = None
        
        self._initialize_log_file()
    
    def _initialize_log_file(self) -> None:
        """Initialize log file based on resume setting."""
        if not self.resume:
            # Create new log file
            self.log_file = open(self.log_path, 'w', newline='')
            self.logger = csv.writer(self.log_file, delimiter='\t')
            self.logger.writerow(self.header)
        else:
            # Resume from existing log file
            if os.path.exists(self.log_path):
                self.log_file = open(self.log_path, 'a+', newline='')
                self.log_file.seek(0, os.SEEK_SET)
                reader = csv.reader(self.log_file, delimiter='\t')
                try:
                    self.header = next(reader)
                except StopIteration:
                    # Empty file, write header
                    self.log_file.seek(0, os.SEEK_END)
                    self.logger = csv.writer(self.log_file, delimiter='\t')
                    self.logger.writerow(self.header)
                    return
                # Move to end for appending
                self.log_file.seek(0, os.SEEK_END)
            else:
                # File doesn't exist, create new one
                self.log_file = open(self.log_path, 'w', newline='')
                self.logger = csv.writer(self.log_file, delimiter='\t')
                self.logger.writerow(self.header)
                return
            
            self.logger = csv.writer(self.log_file, delimiter='\t')
    
    def log(self, values: Dict[str, Union[float, int, str]]) -> None:
        """
        Log values to CSV file.
        
        Args:
            values (Dict): Dictionary mapping header names to values
        """
        write_values = []
        for tag in self.header:
            if tag not in values:
                raise ValueError(f"Missing value for header '{tag}'")
            write_values.append(values[tag])
        
        self.logger.writerow(write_values)
        self.log_file.flush()
    
    def close(self) -> None:
        """Close log file."""
        if self.log_file:
            self.log_file.close()
    
    def __del__(self):
        """Destructor to ensure file is closed."""
        self.close()


class AverageMeter:
    """
    Computes and stores the average and current value.
    
    Useful for tracking metrics during training (loss, accuracy, etc.).
    """
    
    def __init__(self):
        """Initialize meter."""
        self.reset()
    
    def reset(self) -> None:
        """Reset all statistics."""
        self.val = 0.0
        self.avg = 0.0
        self.sum = 0.0
        self.count = 0
    
    def update(self, val: float, n: int = 1) -> None:
        """
        Update statistics with new value.
        
        Args:
            val (float): New value
            n (int): Number of samples this value represents
        """
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count if self.count > 0 else 0.0
    
    def __str__(self) -> str:
        """String representation."""
        return f"AverageMeter(val={self.val:.4f}, avg={self.avg:.4f}, count={self.count})"


# ============================================================================
# MODEL UTILITIES
# ============================================================================

def construct_depth_model(base_model: nn.Module) -> nn.Module:
    """
    Modify model's first convolution layer for depth input.
    
    Converts RGB input model to single-channel depth input by averaging
    the RGB channels of the first convolution layer.
    
    Args:
        base_model (nn.Module): Base model to modify
        
    Returns:
        nn.Module: Modified model for depth input
    """
    modules = list(base_model.modules())
    
    # Find first Conv3d layer
    conv3d_indices = [i for i, module in enumerate(modules) 
                      if isinstance(module, nn.Conv3d)]
    
    if not conv3d_indices:
        print("Warning: No Conv3d layers found in model")
        return base_model
    
    first_conv_idx = conv3d_indices[0]
    conv_layer = modules[first_conv_idx]
    container = modules[first_conv_idx - 1]
    
    # Get current parameters
    params = list(conv_layer.parameters())
    if not params:
        print("Warning: No parameters found in first conv layer")
        return base_model
    
    # Create new single-channel kernel by averaging RGB channels
    old_weight = params[0].data
    kernel_size = old_weight.size()
    
    # Average across input channels (RGB -> single channel)
    new_weight = old_weight.mean(dim=1, keepdim=True)
    
    # Create new convolution layer
    new_conv = nn.Conv3d(
        in_channels=1,
        out_channels=conv_layer.out_channels,
        kernel_size=conv_layer.kernel_size,
        stride=conv_layer.stride,
        padding=conv_layer.padding,
        bias=len(params) == 2
    )
    
    # Set new weights
    new_conv.weight.data = new_weight
    if len(params) == 2:  # Has bias
        new_conv.bias.data = params[1].data
    
    # Replace the layer in container
    # Get layer name by removing '.weight' suffix from state dict key
    layer_name = list(container.state_dict().keys())[0][:-7]
    setattr(container, layer_name, new_conv)
    
    return base_model


# For backward compatibility
_construct_depth_model = construct_depth_model


# ============================================================================
# DATA PROCESSING
# ============================================================================

def get_fusion_label(csv_path: str) -> np.ndarray:
    """
    Read CSV file and return ground truth labels.
    
    Args:
        csv_path (str): Path to CSV label file
        
    Returns:
        np.ndarray: Ground truth labels array
    """
    # Initialize large array for labels (assuming max 360k frames)
    gt = np.zeros(360000, dtype=np.int32)
    base = -10000
    
    try:
        with open(csv_path, 'r') as csv_file:
            csv_reader = csv.reader(csv_file, delimiter=',')
            for row in csv_reader:
                if not row or row[-1] == '':
                    continue
                
                # Update base index when new video starts
                if len(row) > 1 and row[1] != '':
                    base += 10000
                
                # Set normal driving labels
                if len(row) > 4 and row[4] == 'N':
                    start_frame = int(row[2])
                    end_frame = int(row[3])
                    gt[base + start_frame:base + end_frame + 1] = 1
                    
    except FileNotFoundError:
        print(f"Warning: Label file not found: {csv_path}")
        return np.zeros(360000, dtype=np.int32)
    except Exception as e:
        print(f"Warning: Error reading label file: {e}")
        return np.zeros(360000, dtype=np.int32)
    
    return gt


# ============================================================================
# EVALUATION AND METRICS
# ============================================================================

def evaluate_scores(scores: np.ndarray, labels: np.ndarray,
                   plot_roc: bool = False) -> Tuple[float, float, float]:
    """
    Evaluate anomaly detection performance.

    Computes accuracy and AUC by evaluating similarity scores against ground truth.

    Args:
        scores (np.ndarray): Similarity scores for each frame
        labels (np.ndarray): Ground truth labels (1=normal, 0=anomalous)
        plot_roc (bool): Whether to plot ROC curve

    Returns:
        Tuple[float, float, float]: (best_accuracy, best_threshold, auc_score)
    """
    if not HAS_SKLEARN:
        print("Warning: sklearn not available. Cannot compute AUC.")
        return 0.0, 0.0, 0.0

    # Find best threshold by grid search
    thresholds = np.arange(0.0, 1.0, 0.01)
    best_acc = 0.0
    best_threshold = 0.0

    for threshold in thresholds:
        predictions = (scores >= threshold).astype(int)
        correct = (predictions == labels)
        accuracy = np.sum(correct) / len(correct) * 100

        if accuracy > best_acc:
            best_acc = accuracy
            best_threshold = threshold

    # Compute AUC
    try:
        fpr, tpr, _ = metrics.roc_curve(labels, scores, pos_label=1)
        auc_score = auc(fpr, tpr)
    except Exception as e:
        print(f"Warning: Error computing AUC: {e}")
        auc_score = 0.0
        fpr, tpr = np.array([0, 1]), np.array([0, 1])

    # Plot ROC curve if requested
    if plot_roc and HAS_MATPLOTLIB:
        plt.figure(figsize=(8, 6))
        plt.plot(fpr, tpr, color='red', linewidth=2, label=f'ROC Curve (AUC = {auc_score:.3f})')
        plt.plot([0, 1], [0, 1], color='blue', linestyle='--', label='Random Classifier')
        plt.xlabel('False Positive Rate', fontsize=14)
        plt.ylabel('True Positive Rate', fontsize=14)
        plt.title('ROC Curve for Anomaly Detection', fontsize=16)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()
    elif plot_roc and not HAS_MATPLOTLIB:
        print("Warning: matplotlib not available. Cannot plot ROC curve.")

    return best_acc, best_threshold, auc_score


def post_process_scores(scores: np.ndarray, window_size: int = 6) -> np.ndarray:
    """
    Apply temporal smoothing to anomaly scores.

    Uses a sliding window average to smooth scores over time, reducing
    noise and improving temporal consistency.

    Args:
        scores (np.ndarray): Raw anomaly scores
        window_size (int): Size of smoothing window

    Returns:
        np.ndarray: Smoothed scores
    """
    if window_size <= 1:
        return scores.copy()

    processed_scores = np.zeros_like(scores)

    for i in range(len(scores)):
        start_idx = max(0, i - window_size + 1)
        end_idx = i + 1
        processed_scores[i] = np.mean(scores[start_idx:end_idx])

    return processed_scores


# ============================================================================
# SCORE FUSION
# ============================================================================

class ScoreFusion:
    """
    Utility class for fusing scores from multiple modalities and views.

    Supports various fusion strategies for combining scores from:
    - Different views (top, front)
    - Different modalities (depth, IR)
    """

    VALID_MODES = [
        'top_d', 'top_ir', 'front_d', 'front_ir',
        'fusion_top', 'fusion_front', 'fusion_d', 'fusion_ir', 'fusion_all'
    ]

    SCORE_FILES = {
        'top_d': 'score_top_d.npy',
        'top_ir': 'score_top_IR.npy',
        'front_d': 'score_front_d.npy',
        'front_ir': 'score_front_IR.npy'
    }

    @staticmethod
    def load_scores(score_folder: str, mode: str) -> Optional[np.ndarray]:
        """
        Load and optionally fuse scores based on specified mode.

        Args:
            score_folder (str): Folder containing score files
            mode (str): Score loading/fusion mode

        Returns:
            Optional[np.ndarray]: Loaded or fused scores, None if error
        """
        if mode not in ScoreFusion.VALID_MODES:
            print(f"Error: Invalid mode '{mode}'. Valid modes: {ScoreFusion.VALID_MODES}")
            return None

        try:
            if mode in ScoreFusion.SCORE_FILES:
                # Load single modality scores
                score_file = ScoreFusion.SCORE_FILES[mode]
                score_path = os.path.join(score_folder, score_file)
                return np.load(score_path)

            elif mode == 'fusion_top':
                # Fuse top view scores (depth + IR)
                top_d = np.load(os.path.join(score_folder, ScoreFusion.SCORE_FILES['top_d']))
                top_ir = np.load(os.path.join(score_folder, ScoreFusion.SCORE_FILES['top_ir']))
                return np.mean([top_d, top_ir], axis=0)

            elif mode == 'fusion_front':
                # Fuse front view scores (depth + IR)
                front_d = np.load(os.path.join(score_folder, ScoreFusion.SCORE_FILES['front_d']))
                front_ir = np.load(os.path.join(score_folder, ScoreFusion.SCORE_FILES['front_ir']))
                return np.mean([front_d, front_ir], axis=0)

            elif mode == 'fusion_d':
                # Fuse depth modality scores (top + front)
                top_d = np.load(os.path.join(score_folder, ScoreFusion.SCORE_FILES['top_d']))
                front_d = np.load(os.path.join(score_folder, ScoreFusion.SCORE_FILES['front_d']))
                return np.mean([top_d, front_d], axis=0)

            elif mode == 'fusion_ir':
                # Fuse IR modality scores (top + front)
                top_ir = np.load(os.path.join(score_folder, ScoreFusion.SCORE_FILES['top_ir']))
                front_ir = np.load(os.path.join(score_folder, ScoreFusion.SCORE_FILES['front_ir']))
                return np.mean([top_ir, front_ir], axis=0)

            elif mode == 'fusion_all':
                # Fuse all modality and view scores
                scores = []
                for score_file in ScoreFusion.SCORE_FILES.values():
                    score_path = os.path.join(score_folder, score_file)
                    scores.append(np.load(score_path))
                return np.mean(scores, axis=0)

        except FileNotFoundError as e:
            print(f"Error: Score file not found: {e}")
            return None
        except Exception as e:
            print(f"Error loading scores: {e}")
            return None


# For backward compatibility
def get_score(score_folder: str, mode: str) -> Optional[np.ndarray]:
    """
    Legacy function for loading scores. Use ScoreFusion.load_scores instead.

    Args:
        score_folder (str): Folder containing score files
        mode (str): Score loading/fusion mode

    Returns:
        Optional[np.ndarray]: Loaded or fused scores
    """
    return ScoreFusion.load_scores(score_folder, mode)


# For backward compatibility
evaluate = evaluate_scores
post_process = post_process_scores


# ============================================================================
# TESTING
# ============================================================================

def test_utilities():
    """Test utility functions."""
    print("Testing Driver Anomaly Detection Utilities...")

    # Test tensor operations
    print("\n1. Testing tensor operations...")
    x = torch.randn(4, 128)
    normalized = l2_normalize(x)
    print(f"Original tensor norm: {torch.norm(x, dim=1)}")
    print(f"Normalized tensor norm: {torch.norm(normalized, dim=1)}")

    # Test AverageMeter
    print("\n2. Testing AverageMeter...")
    meter = AverageMeter()
    for i in range(5):
        meter.update(i * 0.1)
    print(f"AverageMeter: {meter}")

    # Test score processing
    print("\n3. Testing score processing...")
    dummy_scores = np.random.rand(100)
    smoothed = post_process_scores(dummy_scores, window_size=5)
    print(f"Original scores shape: {dummy_scores.shape}")
    print(f"Smoothed scores shape: {smoothed.shape}")
    print(f"Score difference (mean): {np.mean(np.abs(dummy_scores - smoothed)):.4f}")

    # Test evaluation (if sklearn available)
    if HAS_SKLEARN:
        print("\n4. Testing evaluation...")
        dummy_labels = np.random.randint(0, 2, 100)
        dummy_scores = np.random.rand(100)
        acc, thresh, auc_val = evaluate_scores(dummy_scores, dummy_labels)
        print(f"Dummy evaluation - Accuracy: {acc:.2f}%, Threshold: {thresh:.3f}, AUC: {auc_val:.3f}")

    print("\nUtility tests completed!")


if __name__ == '__main__':
    test_utilities()
